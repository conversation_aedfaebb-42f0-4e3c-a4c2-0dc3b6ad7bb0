# ⚡ QUICK START - BÀI TOÁN TSP

## 🚀 Chạy ngay trong 30 giây

### Bước 1: Mở terminal/command prompt
```bash
cd TSP_Report
```

### Bước 2: Ch<PERSON>y demo
```bash
python demo.py
```

### Bước 3: Chạy chương trình chính
```bash
python src/tsp_solver.py
```

## 📋 Menu nhanh

### Khi chạy chương trình:
1. **Chọn dữ liệu**: `3` (<PERSON><PERSON> liệu mẫu) → `1` (4 thành phố)
2. **Chọn thuật toán**: `3` (So sánh cả hai)
3. **Xem kết quả**: Tự động hiển thị
4. **Lưu file**: `y` (có) hoặc `n` (không)

## 🎯 Kết quả mong đợi

```
=== KẾT QUẢ THUẬT TOÁN BRUTE-FORCE ===
Chu trình tối ưu: 0 → 3 → 1 → 2 → 0
Chi phí tối ưu: 80
Thời gian thực thi: 0.001 giây

=== KẾT QUẢ THUẬT TOÁN NEAREST NEIGHBOR ===
Chu trình tìm được: 0 → 3 → 1 → 2 → 0
Chi phí: 80
Thời gian thực thi: 0.0001 giây

=== SO SÁNH KẾT QUẢ ===
Nearest Neighbor nhanh hơn 10 lần!
```

## 🔧 Nếu có lỗi

### Lỗi "python không được nhận diện":
```bash
# Thử:
py demo.py
# hoặc:
python3 demo.py
```

### Lỗi "ModuleNotFoundError":
```bash
# Đảm bảo đang ở thư mục đúng:
cd TSP_Report
python demo.py
```

## 📚 Đọc thêm

- **Báo cáo đầy đủ**: `report/TSP_Report.md`
- **Hướng dẫn chi tiết**: `HUONG_DAN.md`
- **Tổng quan**: `README.md`

---

**🎉 Chúc bạn thành công!**
