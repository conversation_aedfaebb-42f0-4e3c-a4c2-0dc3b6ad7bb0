"""
Thuật toán Nearest Neighbor cho bài toán TSP
"""

from typing import List, Tuple
from utils import calculate_tour_cost

def nearest_neighbor_tsp(distance_matrix: List[List[int]], start_city: int = 0) -> <PERSON><PERSON>[List[int], int]:
    """
    G<PERSON><PERSON>i bài toán TSP bằng thuật toán Láng giềng gần nhất (Nearest Neighbor)
    
    Thuật toán này bắt đầu từ một thành phố, sau đó luôn di chuyển đến
    thành phố gần nhất chưa được thăm cho đến khi thăm hết tất cả các thành phố.
    
    Args:
        distance_matrix: Ma trận khoảng cách n×n
        start_city: Thành phố xuất phát (mặc định là 0)
    
    Returns:
        Tuple (chu_trình_tìm_được, chi_phí_chu_trình)
    
    <PERSON><PERSON> phức tạp:
        - Thời gian: O(n²) - với mỗi thành phố, tìm thành phố gần nhất trong O(n)
        - <PERSON>h<PERSON><PERSON> gian: O(n) - để lưu trạng thái thăm và chu trình
    """
    n = len(distance_matrix)
    
    if n <= 1:
        return [start_city], 0
    
    if n == 2:
        other_city = 1 - start_city
        cost = distance_matrix[start_city][other_city] * 2
        return [start_city, other_city, start_city], cost
    
    # Khởi tạo
    visited = [False] * n
    tour = [start_city]
    visited[start_city] = True
    current_city = start_city
    total_cost = 0
    
    # Thăm n-1 thành phố còn lại
    for step in range(n - 1):
        nearest_city = -1
        min_distance = float('inf')
        
        # Tìm thành phố gần nhất chưa được thăm
        for next_city in range(n):
            if not visited[next_city]:
                distance = distance_matrix[current_city][next_city]
                if distance < min_distance:
                    min_distance = distance
                    nearest_city = next_city
        
        # Di chuyển đến thành phố gần nhất
        if nearest_city != -1:
            tour.append(nearest_city)
            visited[nearest_city] = True
            total_cost += min_distance
            current_city = nearest_city
    
    # Quay về thành phố xuất phát
    return_cost = distance_matrix[current_city][start_city]
    tour.append(start_city)
    total_cost += return_cost
    
    return tour, total_cost

def nearest_neighbor_all_starts(distance_matrix: List[List[int]]) -> Tuple[List[int], int]:
    """
    Chạy thuật toán Nearest Neighbor từ tất cả các thành phố xuất phát
    và chọn kết quả tốt nhất
    
    Args:
        distance_matrix: Ma trận khoảng cách n×n
    
    Returns:
        Tuple (chu_trình_tốt_nhất, chi_phí_tốt_nhất)
    """
    n = len(distance_matrix)
    best_tour = None
    best_cost = float('inf')
    best_start = 0
    
    # Thử từ tất cả các thành phố xuất phát
    for start_city in range(n):
        tour, cost = nearest_neighbor_tsp(distance_matrix, start_city)
        
        if cost < best_cost:
            best_cost = cost
            best_tour = tour.copy()
            best_start = start_city
    
    print(f"Thành phố xuất phát tốt nhất: {best_start}")
    return best_tour, best_cost

def nearest_neighbor_with_details(distance_matrix: List[List[int]], start_city: int = 0) -> Tuple[List[int], int, List[str]]:
    """
    Phiên bản chi tiết của thuật toán Nearest Neighbor với thông tin từng bước
    
    Args:
        distance_matrix: Ma trận khoảng cách n×n
        start_city: Thành phố xuất phát
    
    Returns:
        Tuple (chu_trình, chi_phí, danh_sách_bước_thực_hiện)
    """
    n = len(distance_matrix)
    
    if n <= 1:
        return [start_city], 0, ["Chỉ có 1 thành phố"]
    
    visited = [False] * n
    tour = [start_city]
    visited[start_city] = True
    current_city = start_city
    total_cost = 0
    steps = []
    
    steps.append(f"Bước 0: Bắt đầu tại thành phố {start_city}")
    
    # Thăm n-1 thành phố còn lại
    for step in range(n - 1):
        nearest_city = -1
        min_distance = float('inf')
        
        # Tìm thành phố gần nhất chưa được thăm
        candidates = []
        for next_city in range(n):
            if not visited[next_city]:
                distance = distance_matrix[current_city][next_city]
                candidates.append((next_city, distance))
                if distance < min_distance:
                    min_distance = distance
                    nearest_city = next_city
        
        # Ghi lại thông tin bước
        candidates_str = ", ".join([f"thành phố {city} (khoảng cách {dist})" for city, dist in candidates])
        steps.append(f"Bước {step + 1}: Từ thành phố {current_city}")
        steps.append(f"  - Các lựa chọn: {candidates_str}")
        steps.append(f"  - Chọn thành phố {nearest_city} (gần nhất với khoảng cách {min_distance})")
        
        # Di chuyển đến thành phố gần nhất
        tour.append(nearest_city)
        visited[nearest_city] = True
        total_cost += min_distance
        current_city = nearest_city
    
    # Quay về thành phố xuất phát
    return_cost = distance_matrix[current_city][start_city]
    tour.append(start_city)
    total_cost += return_cost
    
    steps.append(f"Bước cuối: Quay về thành phố xuất phát {start_city} (khoảng cách {return_cost})")
    steps.append(f"Tổng chi phí: {total_cost}")
    
    return tour, total_cost, steps

def analyze_nearest_neighbor_quality(distance_matrix: List[List[int]], optimal_cost: int = None) -> dict:
    """
    Phân tích chất lượng của thuật toán Nearest Neighbor
    
    Args:
        distance_matrix: Ma trận khoảng cách
        optimal_cost: Chi phí tối ưu (nếu biết)
    
    Returns:
        Dictionary chứa thông tin phân tích
    """
    n = len(distance_matrix)
    results = []
    
    # Chạy từ tất cả các thành phố xuất phát
    for start in range(n):
        tour, cost = nearest_neighbor_tsp(distance_matrix, start)
        results.append({
            'start_city': start,
            'tour': tour,
            'cost': cost
        })
    
    # Tính toán thống kê
    costs = [result['cost'] for result in results]
    best_cost = min(costs)
    worst_cost = max(costs)
    avg_cost = sum(costs) / len(costs)
    
    analysis = {
        'num_cities': n,
        'results': results,
        'best_cost': best_cost,
        'worst_cost': worst_cost,
        'average_cost': avg_cost,
        'cost_variance': worst_cost - best_cost,
        'best_starts': [r['start_city'] for r in results if r['cost'] == best_cost],
        'worst_starts': [r['start_city'] for r in results if r['cost'] == worst_cost]
    }
    
    if optimal_cost is not None:
        analysis['optimal_cost'] = optimal_cost
        analysis['best_approximation_ratio'] = best_cost / optimal_cost
        analysis['worst_approximation_ratio'] = worst_cost / optimal_cost
        analysis['average_approximation_ratio'] = avg_cost / optimal_cost
    
    return analysis

def print_nearest_neighbor_analysis(analysis: dict):
    """
    In kết quả phân tích thuật toán Nearest Neighbor
    
    Args:
        analysis: Dictionary kết quả từ analyze_nearest_neighbor_quality
    """
    print("=== PHÂN TÍCH THUẬT TOÁN NEAREST NEIGHBOR ===")
    print(f"Số thành phố: {analysis['num_cities']}")
    print(f"Chi phí tốt nhất: {analysis['best_cost']}")
    print(f"Chi phí tệ nhất: {analysis['worst_cost']}")
    print(f"Chi phí trung bình: {analysis['average_cost']:.2f}")
    print(f"Độ chênh lệch: {analysis['cost_variance']}")
    print(f"Thành phố xuất phát tốt nhất: {analysis['best_starts']}")
    print(f"Thành phố xuất phát tệ nhất: {analysis['worst_starts']}")
    
    if 'optimal_cost' in analysis:
        print(f"\nSo với lời giải tối ưu ({analysis['optimal_cost']}):")
        print(f"Tỷ lệ xấp xỉ tốt nhất: {analysis['best_approximation_ratio']:.3f} ({analysis['best_approximation_ratio']*100:.1f}%)")
        print(f"Tỷ lệ xấp xỉ tệ nhất: {analysis['worst_approximation_ratio']:.3f} ({analysis['worst_approximation_ratio']*100:.1f}%)")
        print(f"Tỷ lệ xấp xỉ trung bình: {analysis['average_approximation_ratio']:.3f} ({analysis['average_approximation_ratio']*100:.1f}%)")

def nearest_neighbor_2opt_improvement(distance_matrix: List[List[int]], initial_tour: List[int] = None) -> Tuple[List[int], int]:
    """
    Cải thiện chu trình từ Nearest Neighbor bằng thuật toán 2-opt
    
    Args:
        distance_matrix: Ma trận khoảng cách
        initial_tour: Chu trình ban đầu (nếu None thì dùng Nearest Neighbor)
    
    Returns:
        Tuple (chu_trình_cải_thiện, chi_phí_mới)
    """
    if initial_tour is None:
        initial_tour, _ = nearest_neighbor_tsp(distance_matrix)
    
    n = len(distance_matrix)
    tour = initial_tour[:-1]  # Loại bỏ thành phố cuối (trùng với thành phố đầu)
    improved = True
    iterations = 0
    
    while improved:
        improved = False
        iterations += 1
        
        for i in range(n):
            for j in range(i + 2, n):
                # Thử hoán đổi cạnh (i, i+1) và (j, j+1)
                if j == n - 1:
                    continue
                
                # Tính chi phí hiện tại
                current_cost = (distance_matrix[tour[i]][tour[i + 1]] + 
                               distance_matrix[tour[j]][tour[(j + 1) % n]])
                
                # Tính chi phí sau khi hoán đổi
                new_cost = (distance_matrix[tour[i]][tour[j]] + 
                           distance_matrix[tour[i + 1]][tour[(j + 1) % n]])
                
                # Nếu cải thiện được, thực hiện hoán đổi
                if new_cost < current_cost:
                    # Đảo ngược đoạn từ i+1 đến j
                    tour[i + 1:j + 1] = tour[i + 1:j + 1][::-1]
                    improved = True
    
    # Thêm thành phố xuất phát vào cuối
    final_tour = tour + [tour[0]]
    final_cost = calculate_tour_cost(final_tour, distance_matrix)
    
    print(f"Số lần lặp 2-opt: {iterations}")
    return final_tour, final_cost

if __name__ == "__main__":
    # Test với một ví dụ nhỏ
    test_matrix = [
        [0, 10, 15, 5],
        [10, 0, 35, 25],
        [15, 35, 0, 30],
        [5, 25, 30, 0]
    ]
    
    print("Ma trận test:")
    for i, row in enumerate(test_matrix):
        print(f"  {i}: {row}")
    
    print("\n=== NEAREST NEIGHBOR THÔNG THƯỜNG ===")
    tour, cost = nearest_neighbor_tsp(test_matrix, 0)
    print(f"Chu trình: {' → '.join(map(str, tour))}")
    print(f"Chi phí: {cost}")
    
    print("\n=== NEAREST NEIGHBOR CHI TIẾT ===")
    tour, cost, steps = nearest_neighbor_with_details(test_matrix, 0)
    for step in steps:
        print(step)
    
    print("\n=== NEAREST NEIGHBOR TỪ TẤT CẢ XUẤT PHÁT ===")
    best_tour, best_cost = nearest_neighbor_all_starts(test_matrix)
    print(f"Chu trình tốt nhất: {' → '.join(map(str, best_tour))}")
    print(f"Chi phí tốt nhất: {best_cost}")
    
    print("\n=== PHÂN TÍCH CHẤT LƯỢNG ===")
    analysis = analyze_nearest_neighbor_quality(test_matrix, optimal_cost=80)
    print_nearest_neighbor_analysis(analysis)
    
    print("\n=== CẢI THIỆN BẰNG 2-OPT ===")
    improved_tour, improved_cost = nearest_neighbor_2opt_improvement(test_matrix)
    print(f"Chu trình sau cải thiện: {' → '.join(map(str, improved_tour))}")
    print(f"Chi phí sau cải thiện: {improved_cost}")
