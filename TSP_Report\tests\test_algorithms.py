"""
<PERSON><PERSON><PERSON> thử các thuật toán TSP
"""

import sys
import os
import time

# Thê<PERSON> thư mục src vào path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'data'))

from brute_force import brute_force_tsp, brute_force_tsp_with_pruning
from nearest_neighbor import nearest_neighbor_tsp, nearest_neighbor_all_starts
from utils import calculate_tour_cost, measure_execution_time
from test_cases import ALL_TEST_CASES, validate_test_case

def test_brute_force():
    """<PERSON><PERSON><PERSON> thử thuật toán Brute-force"""
    print("=== KIỂM THỬ THUẬT TOÁN BRUTE-FORCE ===")
    
    passed = 0
    total = 0
    
    for test_case in ALL_TEST_CASES:
        if len(test_case['matrix']) > 7:  # Bỏ qua test case quá lớn
            continue
            
        total += 1
        matrix = test_case['matrix']
        expected_cost = test_case['optimal_cost']
        
        print(f"\nTest: {test_case['name']}")
        
        try:
            # <PERSON><PERSON><PERSON> thuật toán
            tour, execution_time = measure_execution_time(brute_force_tsp, matrix)
            actual_cost = tour[1]
            actual_tour = tour[0]
            
            # Kiểm tra kết quả
            if actual_cost == expected_cost:
                print(f"✓ PASS - Chi phí: {actual_cost}, Thời gian: {execution_time:.4f}s")
                passed += 1
            else:
                print(f"✗ FAIL - Mong đợi: {expected_cost}, Thực tế: {actual_cost}")
                
        except Exception as e:
            print(f"✗ ERROR - {str(e)}")
    
    print(f"\nKết quả Brute-force: {passed}/{total} test cases passed")
    return passed == total

def test_nearest_neighbor():
    """Kiểm thử thuật toán Nearest Neighbor"""
    print("\n=== KIỂM THỬ THUẬT TOÁN NEAREST NEIGHBOR ===")
    
    passed = 0
    total = 0
    
    for test_case in ALL_TEST_CASES:
        total += 1
        matrix = test_case['matrix']
        optimal_cost = test_case['optimal_cost']
        
        print(f"\nTest: {test_case['name']}")
        
        try:
            # Chạy thuật toán từ thành phố 0
            tour, execution_time = measure_execution_time(nearest_neighbor_tsp, matrix, 0)
            cost_from_0 = tour[1]
            tour_from_0 = tour[0]
            
            # Chạy từ tất cả thành phố xuất phát
            best_tour, execution_time_all = measure_execution_time(nearest_neighbor_all_starts, matrix)
            best_cost = best_tour[1]
            best_tour = best_tour[0]
            
            # Tính tỷ lệ xấp xỉ
            ratio_from_0 = cost_from_0 / optimal_cost
            ratio_best = best_cost / optimal_cost
            
            print(f"  Từ thành phố 0: {cost_from_0} (tỷ lệ: {ratio_from_0:.3f})")
            print(f"  Tốt nhất: {best_cost} (tỷ lệ: {ratio_best:.3f})")
            print(f"  Thời gian: {execution_time:.4f}s / {execution_time_all:.4f}s")
            
            # Kiểm tra tính hợp lệ của chu trình
            if is_valid_tour(tour_from_0, len(matrix)) and is_valid_tour(best_tour, len(matrix)):
                print(f"✓ PASS - Chu trình hợp lệ")
                passed += 1
            else:
                print(f"✗ FAIL - Chu trình không hợp lệ")
                
        except Exception as e:
            print(f"✗ ERROR - {str(e)}")
    
    print(f"\nKết quả Nearest Neighbor: {passed}/{total} test cases passed")
    return passed == total

def test_algorithm_consistency():
    """Kiểm thử tính nhất quán của thuật toán"""
    print("\n=== KIỂM THỬ TÍNH NHẤT QUÁN ===")
    
    passed = 0
    total = 0
    
    for test_case in ALL_TEST_CASES:
        if len(test_case['matrix']) > 6:  # Chỉ test với bài toán nhỏ
            continue
            
        total += 1
        matrix = test_case['matrix']
        
        print(f"\nTest: {test_case['name']}")
        
        try:
            # Chạy Brute-force nhiều lần
            results = []
            for _ in range(3):
                tour, _ = brute_force_tsp(matrix)
                results.append(tour[1])  # Chi phí
            
            # Kiểm tra tất cả kết quả có giống nhau không
            if all(cost == results[0] for cost in results):
                print(f"✓ PASS - Brute-force nhất quán: {results[0]}")
                passed += 1
            else:
                print(f"✗ FAIL - Brute-force không nhất quán: {results}")
                
        except Exception as e:
            print(f"✗ ERROR - {str(e)}")
    
    print(f"\nKết quả tính nhất quán: {passed}/{total} test cases passed")
    return passed == total

def test_performance():
    """Kiểm thử hiệu suất thuật toán"""
    print("\n=== KIỂM THỬ HIỆU SUẤT ===")
    
    # Test với các kích thước khác nhau
    sizes = [4, 5, 6, 7, 8]
    
    print(f"{'Kích thước':<10} {'Brute-force (s)':<15} {'Nearest Neighbor (s)':<20} {'Tỷ lệ tốc độ':<15}")
    print("-" * 65)
    
    for n in sizes:
        # Tìm test case có kích thước n
        test_case = None
        for tc in ALL_TEST_CASES:
            if len(tc['matrix']) == n:
                test_case = tc
                break
        
        if test_case is None:
            continue
        
        matrix = test_case['matrix']
        
        try:
            # Đo thời gian Brute-force
            if n <= 7:  # Chỉ chạy Brute-force với n nhỏ
                _, bf_time = measure_execution_time(brute_force_tsp, matrix)
            else:
                bf_time = float('inf')
            
            # Đo thời gian Nearest Neighbor
            _, nn_time = measure_execution_time(nearest_neighbor_tsp, matrix)
            
            # Tính tỷ lệ tốc độ
            if bf_time != float('inf'):
                speed_ratio = bf_time / nn_time
                print(f"{n:<10} {bf_time:<15.6f} {nn_time:<20.6f} {speed_ratio:<15.1f}x")
            else:
                print(f"{n:<10} {'> 60s':<15} {nn_time:<20.6f} {'> 1000x':<15}")
                
        except Exception as e:
            print(f"{n:<10} ERROR: {str(e)}")

def test_edge_cases():
    """Kiểm thử các trường hợp biên"""
    print("\n=== KIỂM THỬ TRƯỜNG HỢP BIÊN ===")
    
    # Test case 1: Ma trận 2x2
    print("\nTest 1: Ma trận 2x2")
    matrix_2x2 = [[0, 5], [5, 0]]
    try:
        tour_bf, _ = brute_force_tsp(matrix_2x2)
        tour_nn, _ = nearest_neighbor_tsp(matrix_2x2)
        print(f"✓ PASS - Brute-force: {tour_bf[1]}, Nearest Neighbor: {tour_nn[1]}")
    except Exception as e:
        print(f"✗ ERROR - {str(e)}")
    
    # Test case 2: Ma trận với khoảng cách 0 (ngoại trừ đường chéo)
    print("\nTest 2: Ma trận với khoảng cách 0")
    matrix_zero = [[0, 0, 0], [0, 0, 0], [0, 0, 0]]
    try:
        tour_bf, _ = brute_force_tsp(matrix_zero)
        tour_nn, _ = nearest_neighbor_tsp(matrix_zero)
        print(f"✓ PASS - Brute-force: {tour_bf[1]}, Nearest Neighbor: {tour_nn[1]}")
    except Exception as e:
        print(f"✗ ERROR - {str(e)}")
    
    # Test case 3: Ma trận với giá trị lớn
    print("\nTest 3: Ma trận với giá trị lớn")
    matrix_large = [
        [0, 1000000, 999999],
        [1000000, 0, 1000001],
        [999999, 1000001, 0]
    ]
    try:
        tour_bf, _ = brute_force_tsp(matrix_large)
        tour_nn, _ = nearest_neighbor_tsp(matrix_large)
        print(f"✓ PASS - Brute-force: {tour_bf[1]}, Nearest Neighbor: {tour_nn[1]}")
    except Exception as e:
        print(f"✗ ERROR - {str(e)}")

def is_valid_tour(tour, n):
    """
    Kiểm tra tính hợp lệ của một chu trình
    
    Args:
        tour: Chu trình cần kiểm tra
        n: Số lượng thành phố
    
    Returns:
        True nếu chu trình hợp lệ, False nếu không
    """
    if len(tour) != n + 1:
        return False
    
    if tour[0] != tour[-1]:
        return False
    
    visited = set(tour[:-1])
    if len(visited) != n or visited != set(range(n)):
        return False
    
    return True

def compare_algorithms_on_all_tests():
    """So sánh hiệu quả của các thuật toán trên tất cả test cases"""
    print("\n=== SO SÁNH THUẬT TOÁN TRÊN TẤT CẢ TEST CASES ===")
    
    print(f"{'Test Case':<25} {'Optimal':<8} {'BF':<8} {'NN':<8} {'NN Best':<8} {'Ratio':<8}")
    print("-" * 75)
    
    for test_case in ALL_TEST_CASES:
        matrix = test_case['matrix']
        optimal = test_case['optimal_cost']
        name = test_case['name'][:24]
        
        try:
            # Brute-force (chỉ với n <= 7)
            if len(matrix) <= 7:
                bf_tour, _ = brute_force_tsp(matrix)
                bf_cost = bf_tour[1]
            else:
                bf_cost = "N/A"
            
            # Nearest Neighbor từ thành phố 0
            nn_tour, _ = nearest_neighbor_tsp(matrix, 0)
            nn_cost = nn_tour[1]
            
            # Nearest Neighbor tốt nhất
            nn_best_tour, _ = nearest_neighbor_all_starts(matrix)
            nn_best_cost = nn_best_tour[1]
            
            # Tỷ lệ xấp xỉ
            ratio = f"{nn_best_cost/optimal:.2f}"
            
            print(f"{name:<25} {optimal:<8} {bf_cost:<8} {nn_cost:<8} {nn_best_cost:<8} {ratio:<8}")
            
        except Exception as e:
            print(f"{name:<25} ERROR: {str(e)}")

def run_all_tests():
    """Chạy tất cả các test"""
    print("=" * 70)
    print("CHẠY TẤT CẢ CÁC KIỂM THỬ CHO THUẬT TOÁN TSP")
    print("=" * 70)
    
    start_time = time.time()
    
    # Chạy các test
    bf_result = test_brute_force()
    nn_result = test_nearest_neighbor()
    consistency_result = test_algorithm_consistency()
    
    # Test hiệu suất
    test_performance()
    
    # Test trường hợp biên
    test_edge_cases()
    
    # So sánh thuật toán
    compare_algorithms_on_all_tests()
    
    end_time = time.time()
    
    # Tổng kết
    print("\n" + "=" * 70)
    print("TỔNG KẾT KIỂM THỬ")
    print("=" * 70)
    print(f"Brute-force: {'PASS' if bf_result else 'FAIL'}")
    print(f"Nearest Neighbor: {'PASS' if nn_result else 'FAIL'}")
    print(f"Tính nhất quán: {'PASS' if consistency_result else 'FAIL'}")
    print(f"Tổng thời gian kiểm thử: {end_time - start_time:.2f} giây")
    
    if bf_result and nn_result and consistency_result:
        print("\n🎉 TẤT CẢ KIỂM THỬ ĐỀU PASS!")
    else:
        print("\n⚠️  CÓ KIỂM THỬ FAIL, CẦN KIỂM TRA LẠI!")

if __name__ == "__main__":
    run_all_tests()
