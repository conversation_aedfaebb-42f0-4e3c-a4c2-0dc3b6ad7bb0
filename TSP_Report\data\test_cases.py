"""
<PERSON><PERSON><PERSON> bộ dữ liệu kiểm thử cho chương trình TSP
"""

# Test case 1: 4 thành phố - <PERSON><PERSON><PERSON> to<PERSON> cơ bản
TEST_4_CITIES = {
    "name": "4 thành phố - <PERSON><PERSON> bản",
    "matrix": [
        [0, 10, 15, 5],
        [10, 0, 35, 25],
        [15, 35, 0, 30],
        [5, 25, 30, 0]
    ],
    "optimal_cost": 80,
    "optimal_tour": [0, 3, 1, 2, 0],
    "description": "<PERSON><PERSON><PERSON> to<PERSON> TSP cơ bản với 4 thành phố. Chu trình tối ưu: 0→3→1→2→0"
}

# Test case 2: 5 thành phố - Trung bình
TEST_5_CITIES = {
    "name": "5 thành phố - Trung bình",
    "matrix": [
        [0, 12, 10, 19, 8],
        [12, 0, 3, 7, 2],
        [10, 3, 0, 6, 20],
        [19, 7, 6, 0, 4],
        [8, 2, 20, 4, 0]
    ],
    "optimal_cost": 32,
    "optimal_tour": [0, 2, 1, 4, 3, 0],
    "description": "<PERSON><PERSON><PERSON> to<PERSON> TSP với 5 thành phố, c<PERSON> nhiều lựa ch<PERSON>n gần nhau"
}

# Test case 3: 6 thành phố - <PERSON><PERSON>c tạp hơn
TEST_6_CITIES = {
    "name": "6 thành phố - Phức tạp",
    "matrix": [
        [0, 20, 42, 25, 30, 15],
        [20, 0, 30, 34, 15, 25],
        [42, 30, 0, 12, 45, 35],
        [25, 34, 12, 0, 20, 30],
        [30, 15, 45, 20, 0, 18],
        [15, 25, 35, 30, 18, 0]
    ],
    "optimal_cost": 105,
    "optimal_tour": [0, 5, 4, 1, 2, 3, 0],
    "description": "Bài toán TSP với 6 thành phố, thử thách cho thuật toán heuristic"
}

# Test case 4: Trường hợp đặc biệt - Ma trận đối xứng hoàn hảo
TEST_SYMMETRIC = {
    "name": "Ma trận đối xứng hoàn hảo",
    "matrix": [
        [0, 1, 2, 3],
        [1, 0, 1, 2],
        [2, 1, 0, 1],
        [3, 2, 1, 0]
    ],
    "optimal_cost": 4,
    "optimal_tour": [0, 1, 2, 3, 0],
    "description": "Ma trận với khoảng cách tăng dần, dễ dự đoán kết quả"
}

# Test case 5: Trường hợp khó cho Nearest Neighbor
TEST_HARD_FOR_NN = {
    "name": "Khó cho Nearest Neighbor",
    "matrix": [
        [0, 1, 100, 100, 100],
        [1, 0, 1, 100, 100],
        [100, 1, 0, 1, 100],
        [100, 100, 1, 0, 1],
        [100, 100, 100, 1, 0]
    ],
    "optimal_cost": 5,
    "optimal_tour": [0, 1, 2, 3, 4, 0],
    "description": "Trường hợp Nearest Neighbor có thể cho kết quả tệ do tham lam cục bộ"
}

# Test case 6: 7 thành phố - Giới hạn của Brute-force
TEST_7_CITIES = {
    "name": "7 thành phố - Giới hạn Brute-force",
    "matrix": [
        [0, 29, 20, 21, 16, 31, 100],
        [29, 0, 15, 29, 28, 40, 72],
        [20, 15, 0, 15, 14, 25, 81],
        [21, 29, 15, 0, 4, 12, 92],
        [16, 28, 14, 4, 0, 16, 94],
        [31, 40, 25, 12, 16, 0, 95],
        [100, 72, 81, 92, 94, 95, 0]
    ],
    "optimal_cost": 95,
    "optimal_tour": [0, 2, 1, 4, 3, 5, 6, 0],
    "description": "Bài toán 7 thành phố, gần giới hạn thời gian của Brute-force"
}

# Test case 7: Bài toán thực tế - Khoảng cách giữa các thành phố Việt Nam (đơn giản hóa)
TEST_VIETNAM_CITIES = {
    "name": "Thành phố Việt Nam (đơn giản)",
    "matrix": [
        [0, 1760, 1710, 665, 120],     # Hà Nội
        [1760, 0, 340, 1095, 1640],    # TP.HCM  
        [1710, 340, 0, 1055, 1590],    # Đà Nẵng
        [665, 1095, 1055, 0, 545],     # Vinh
        [120, 1640, 1590, 545, 0]      # Thái Nguyên
    ],
    "optimal_cost": 4200,
    "optimal_tour": [0, 4, 3, 2, 1, 0],
    "description": "Khoảng cách thực tế giữa 5 thành phố lớn của Việt Nam (km)"
}

# Danh sách tất cả test cases
ALL_TEST_CASES = [
    TEST_4_CITIES,
    TEST_5_CITIES,
    TEST_6_CITIES,
    TEST_SYMMETRIC,
    TEST_HARD_FOR_NN,
    TEST_7_CITIES,
    TEST_VIETNAM_CITIES
]

def get_test_case(name: str):
    """
    Lấy test case theo tên
    
    Args:
        name: Tên của test case
    
    Returns:
        Dictionary chứa thông tin test case hoặc None nếu không tìm thấy
    """
    for test_case in ALL_TEST_CASES:
        if test_case["name"] == name:
            return test_case
    return None

def get_test_cases_by_size(n: int):
    """
    Lấy tất cả test cases có kích thước n
    
    Args:
        n: Số lượng thành phố
    
    Returns:
        List các test cases có kích thước n
    """
    return [tc for tc in ALL_TEST_CASES if len(tc["matrix"]) == n]

def print_test_case_info(test_case: dict):
    """
    In thông tin chi tiết của một test case
    
    Args:
        test_case: Dictionary chứa thông tin test case
    """
    print(f"Tên: {test_case['name']}")
    print(f"Mô tả: {test_case['description']}")
    print(f"Số thành phố: {len(test_case['matrix'])}")
    print(f"Chi phí tối ưu: {test_case['optimal_cost']}")
    print(f"Chu trình tối ưu: {' → '.join(map(str, test_case['optimal_tour']))}")
    
    print("Ma trận khoảng cách:")
    matrix = test_case['matrix']
    n = len(matrix)
    
    # In header
    print("     ", end="")
    for j in range(n):
        print(f"{j:4}", end="")
    print()
    
    # In các hàng
    for i in range(n):
        print(f"{i:2}   ", end="")
        for j in range(n):
            print(f"{matrix[i][j]:4}", end="")
        print()

def list_all_test_cases():
    """In danh sách tất cả test cases"""
    print("=== DANH SÁCH TẤT CẢ TEST CASES ===")
    for i, test_case in enumerate(ALL_TEST_CASES, 1):
        print(f"{i}. {test_case['name']} ({len(test_case['matrix'])} thành phố)")
        print(f"   {test_case['description']}")
        print()

def validate_test_case(test_case: dict) -> bool:
    """
    Kiểm tra tính hợp lệ của một test case
    
    Args:
        test_case: Dictionary chứa thông tin test case
    
    Returns:
        True nếu test case hợp lệ, False nếu không
    """
    try:
        matrix = test_case['matrix']
        n = len(matrix)
        
        # Kiểm tra ma trận vuông
        for row in matrix:
            if len(row) != n:
                return False
        
        # Kiểm tra đường chéo chính
        for i in range(n):
            if matrix[i][i] != 0:
                return False
        
        # Kiểm tra tính đối xứng
        for i in range(n):
            for j in range(n):
                if matrix[i][j] != matrix[j][i]:
                    return False
        
        # Kiểm tra chu trình tối ưu
        optimal_tour = test_case['optimal_tour']
        if len(optimal_tour) != n + 1:
            return False
        
        if optimal_tour[0] != optimal_tour[-1]:
            return False
        
        # Kiểm tra tất cả thành phố được thăm
        visited_cities = set(optimal_tour[:-1])
        if visited_cities != set(range(n)):
            return False
        
        return True
        
    except (KeyError, TypeError, IndexError):
        return False

def run_test_validation():
    """Chạy kiểm tra tính hợp lệ của tất cả test cases"""
    print("=== KIỂM TRA TÍNH HỢP LỆ CỦA TEST CASES ===")
    
    all_valid = True
    for test_case in ALL_TEST_CASES:
        is_valid = validate_test_case(test_case)
        status = "✓ HỢP LỆ" if is_valid else "✗ KHÔNG HỢP LỆ"
        print(f"{test_case['name']}: {status}")
        
        if not is_valid:
            all_valid = False
    
    if all_valid:
        print("\nTất cả test cases đều hợp lệ!")
    else:
        print("\nCó test cases không hợp lệ, cần kiểm tra lại.")

if __name__ == "__main__":
    # Chạy các test cơ bản
    print("=== KIỂM THỬ MODULE TEST_CASES ===\n")
    
    # Liệt kê tất cả test cases
    list_all_test_cases()
    
    # Kiểm tra tính hợp lệ
    run_test_validation()
    
    # Hiển thị chi tiết một test case
    print("\n=== CHI TIẾT TEST CASE MẪU ===")
    print_test_case_info(TEST_4_CITIES)
