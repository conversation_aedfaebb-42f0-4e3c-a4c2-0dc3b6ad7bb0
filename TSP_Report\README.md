# 🚀 BÁO CÁO ĐỒ ÁN: BÀI TOÁN NGƯỜI BÁN HÀNG (TSP)

## 📖 Tổng quan
Đồ án nghiên cứu và cài đặt minh họa bài toán **Traveling Salesman Problem (TSP)** - một trong những bài toán tối ưu hóa kinh điển và quan trọng nhất trong khoa học máy tính.

### 🎯 Mục tiêu
- Tìm hiểu lý thuyết đồ thị và bài toán TSP
- Cài đặt thuật toán **Brute-force** (tìm lời giải tối ưu)
- Cài đặt thuật toán **Nearest Neighbor** (tìm lời giải gần đúng)
- So sánh hiệu quả các thuật toán

### ⚡ Bắt đầu nhanh
```bash
# Chạy demo
python demo.py

# Chạy chương trình chính
python src/tsp_solver.py

# Chạy kiểm thử
python tests/test_algorithms.py
```

## 📁 Cấu trúc dự án
```
TSP_Report/
├── 📄 README.md                 # File này - Hướng dẫn tổng quan
├── 📄 HUONG_DAN.md             # Hướng dẫn chi tiết cài đặt và sử dụng
├── 🎮 demo.py                  # Demo nhanh các tính năng
├── 📊 report/
│   ├── TSP_Report.md           # 📋 BÁO CÁO CHÍNH (ĐỌC ĐẦU TIÊN)
│   └── images/                 # Hình ảnh minh họa
├── 💻 src/                     # Mã nguồn chương trình
│   ├── tsp_solver.py           # 🎯 Chương trình chính
│   ├── brute_force.py          # 🔍 Thuật toán Brute-force
│   ├── nearest_neighbor.py     # ⚡ Thuật toán Nearest Neighbor
│   └── utils.py                # 🛠️ Các hàm tiện ích
├── 📊 data/                    # Dữ liệu và test cases
│   └── test_cases.py           # Bộ dữ liệu kiểm thử
└── 🧪 tests/
    └── test_algorithms.py      # Kiểm thử thuật toán
```

## 🚀 Hướng dẫn sử dụng

### 1️⃣ Đọc báo cáo (BẮT BUỘC)
```bash
# Mở file báo cáo chính để hiểu lý thuyết
report/TSP_Report.md
```

### 2️⃣ Chạy demo nhanh
```bash
python demo.py
```

### 3️⃣ Sử dụng chương trình đầy đủ
```bash
python src/tsp_solver.py
```

### 4️⃣ Kiểm thử thuật toán
```bash
python tests/test_algorithms.py
```

## 🔧 Yêu cầu hệ thống
- **Python 3.7+** (khuyến nghị 3.8+)
- **Thư viện cơ bản**: itertools, time, random (có sẵn)
- **Thư viện tùy chọn**: numpy, matplotlib

## 📈 Kết quả chính

### 🏆 Thuật toán đã cài đặt
| Thuật toán | Độ phức tạp | Ưu điểm | Nhược điểm |
|------------|-------------|---------|------------|
| **Brute-force** | O(n!) | Tìm lời giải tối ưu | Chỉ khả thi với n ≤ 10 |
| **Nearest Neighbor** | O(n²) | Nhanh, đơn giản | Không đảm bảo tối ưu |

### 📊 Kết quả thực nghiệm
- **Brute-force**: Đảm bảo tối ưu nhưng chậm (tăng giai thừa)
- **Nearest Neighbor**: Nhanh hơn hàng nghìn lần, chất lượng 90-100%
- **Tỷ lệ tốc độ**: Lên đến 12,000x với 10 thành phố

## 🎓 Giá trị học tập

### 📚 Kiến thức đạt được
- Lý thuyết đồ thị và chu trình Hamilton
- Bài toán NP-Hard và độ phức tạp thuật toán
- Sự khác biệt giữa thuật toán chính xác và xấp xỉ
- Kỹ năng phân tích và cài đặt thuật toán

### 💡 Ứng dụng thực tế
- **Logistics**: Tối ưu tuyến đường giao hàng
- **Du lịch**: Lập kế hoạch hành trình
- **Sản xuất**: Tối ưu quy trình dây chuyền
- **Viễn thông**: Thiết kế mạng lưới

## 📖 Tài liệu quan trọng

### 🔥 ĐỌC ĐẦU TIÊN
1. **`report/TSP_Report.md`** - Báo cáo chính với lý thuyết đầy đủ
2. **`HUONG_DAN.md`** - Hướng dẫn cài đặt và sử dụng chi tiết

### 📋 Tài liệu bổ sung
- **`src/`** - Mã nguồn có comment chi tiết
- **`data/test_cases.py`** - Các bộ dữ liệu test
- **`tests/`** - Kiểm thử và validation

## 🎯 Cách sử dụng hiệu quả

### Cho người mới bắt đầu:
1. Đọc `report/TSP_Report.md` để hiểu lý thuyết
2. Chạy `python demo.py` để xem demo
3. Thử `python src/tsp_solver.py` với dữ liệu mẫu

### Cho người có kinh nghiệm:
1. Xem `tests/test_algorithms.py` để hiểu cách test
2. Nghiên cứu mã nguồn trong `src/`
3. Thử nghiệm với dữ liệu tùy chỉnh

## 🏅 Điểm nổi bật

### ✨ Tính năng chính
- ✅ Giao diện console thân thiện
- ✅ Nhiều phương thức nhập dữ liệu
- ✅ So sánh trực quan các thuật toán
- ✅ Phân tích chi tiết kết quả
- ✅ Xuất báo cáo ra file
- ✅ Bộ test cases đa dạng

### 🔬 Chất lượng code
- ✅ Code có cấu trúc rõ ràng
- ✅ Comment chi tiết bằng tiếng Việt
- ✅ Xử lý lỗi toàn diện
- ✅ Test coverage cao
- ✅ Documentation đầy đủ

## 🚀 Hướng phát triển

### 🔮 Tính năng mở rộng
- Giao diện đồ họa (GUI)
- Thuật toán Genetic Algorithm
- Tích hợp với Google Maps
- Trực quan hóa chu trình

### 📈 Cải tiến hiệu suất
- Thuật toán Branch and Bound
- Tối ưu hóa 2-opt, 3-opt
- Xử lý song song (parallel)
- Hỗ trợ bài toán lớn (n > 1000)

---

## 🎉 Kết luận

Đồ án này cung cấp một cái nhìn toàn diện về bài toán TSP, từ lý thuyết đến thực hành. Chương trình không chỉ minh họa các thuật toán mà còn giúp hiểu sâu về độ phức tạp và sự cân bằng giữa thời gian tính toán và chất lượng lời giải.

**🎯 Bắt đầu ngay**: Đọc `report/TSP_Report.md` và chạy `python demo.py`!

---

*📧 Liên hệ: [Thông tin sinh viên]*
*📅 Hoàn thành: [Tháng/Năm]*
*🏫 Trường: [Tên trường]*
