"""
Thuật toán <PERSON>te-force cho bài toán TSP
"""

import itertools
from typing import List, <PERSON><PERSON>
from utils import calculate_tour_cost

def brute_force_tsp(distance_matrix: List[List[int]], start_city: int = 0) -> <PERSON><PERSON>[List[int], int]:
    """
    Giải bài toán TSP bằng thuật toán duyệt toàn bộ (Brute-force)
    
    Thuật toán này liệt kê tất cả các hoán vị có thể của các thành phố
    và chọn chu trình có tổng khoảng cách nhỏ nhất.
    
    Args:
        distance_matrix: Ma trận khoảng cách n×n
        start_city: Thành phố xuất phát (mặc định là 0)
    
    Returns:
        Tuple (chu_trình_tối_ưu, chi_phí_tối_ưu)
    
    Độ phức tạp:
        - <PERSON>h<PERSON><PERSON> gian: O(n!) - do phải duyệt tất cả (n-1)! hoán vị
        - Không gian: O(n) - để lưu chu trình tốt nhất
    """
    n = len(distance_matrix)
    
    if n <= 1:
        return [start_city], 0
    
    if n == 2:
        return [start_city, 1-start_city, start_city], distance_matrix[start_city][1-start_city] * 2
    
    # Danh sách các thành phố cần thăm (loại bỏ thành phố xuất phát)
    cities_to_visit = [i for i in range(n) if i != start_city]
    
    best_tour = None
    best_cost = float('inf')
    total_permutations = 0
    
    # Duyệt tất cả các hoán vị của các thành phố cần thăm
    for permutation in itertools.permutations(cities_to_visit):
        total_permutations += 1
        
        # Tạo chu trình hoàn chỉnh: start -> permutation -> start
        current_tour = [start_city] + list(permutation) + [start_city]
        
        # Tính chi phí của chu trình hiện tại
        current_cost = calculate_tour_cost(current_tour, distance_matrix)
        
        # Cập nhật chu trình tốt nhất nếu tìm được chu trình tốt hơn
        if current_cost < best_cost:
            best_cost = current_cost
            best_tour = current_tour.copy()
    
    print(f"Đã duyệt {total_permutations} hoán vị")
    return best_tour, best_cost

def brute_force_tsp_with_pruning(distance_matrix: List[List[int]], start_city: int = 0) -> Tuple[List[int], int]:
    """
    Phiên bản cải tiến của Brute-force với pruning đơn giản
    
    Thuật toán này sử dụng kỹ thuật cắt tỉa (pruning) để loại bỏ các nhánh
    không triển vọng, giúp giảm thời gian tính toán.
    
    Args:
        distance_matrix: Ma trận khoảng cách n×n
        start_city: Thành phố xuất phát
    
    Returns:
        Tuple (chu_trình_tối_ưu, chi_phí_tối_ưu)
    """
    n = len(distance_matrix)
    
    if n <= 1:
        return [start_city], 0
    
    best_tour = None
    best_cost = float('inf')
    pruned_count = 0
    total_count = 0
    
    def backtrack(current_tour: List[int], current_cost: int, visited: List[bool]):
        nonlocal best_tour, best_cost, pruned_count, total_count
        total_count += 1
        
        # Nếu chi phí hiện tại đã vượt quá chi phí tốt nhất, cắt tỉa
        if current_cost >= best_cost:
            pruned_count += 1
            return
        
        # Nếu đã thăm hết tất cả thành phố
        if len(current_tour) == n + 1:  # +1 vì có thành phố xuất phát ở cuối
            if current_cost < best_cost:
                best_cost = current_cost
                best_tour = current_tour.copy()
            return
        
        # Nếu đã thăm n thành phố, quay về thành phố xuất phát
        if len(current_tour) == n:
            last_city = current_tour[-1]
            return_cost = distance_matrix[last_city][start_city]
            final_tour = current_tour + [start_city]
            final_cost = current_cost + return_cost
            
            if final_cost < best_cost:
                best_cost = final_cost
                best_tour = final_tour.copy()
            return
        
        # Thử thăm các thành phố chưa được thăm
        current_city = current_tour[-1]
        for next_city in range(n):
            if not visited[next_city]:
                # Tính chi phí di chuyển đến thành phố tiếp theo
                move_cost = distance_matrix[current_city][next_city]
                new_cost = current_cost + move_cost
                
                # Pruning: nếu chi phí hiện tại + chi phí tối thiểu để hoàn thành > best_cost
                if new_cost < best_cost:
                    visited[next_city] = True
                    current_tour.append(next_city)
                    
                    backtrack(current_tour, new_cost, visited)
                    
                    # Backtrack
                    current_tour.pop()
                    visited[next_city] = False
                else:
                    pruned_count += 1
    
    # Khởi tạo
    visited = [False] * n
    visited[start_city] = True
    initial_tour = [start_city]
    
    # Bắt đầu backtrack
    backtrack(initial_tour, 0, visited)
    
    print(f"Tổng số nút đã xét: {total_count}")
    print(f"Số nút đã cắt tỉa: {pruned_count}")
    print(f"Tỷ lệ cắt tỉa: {pruned_count/total_count*100:.2f}%")
    
    return best_tour, best_cost

def estimate_brute_force_time(n: int) -> str:
    """
    Ước tính thời gian thực thi của thuật toán Brute-force
    
    Args:
        n: Số lượng thành phố
    
    Returns:
        Chuỗi mô tả thời gian ước tính
    """
    import math
    
    # Số hoán vị cần duyệt
    permutations = math.factorial(n - 1) if n > 1 else 1
    
    # Ước tính thời gian (giả sử xử lý 1 triệu hoán vị/giây)
    operations_per_second = 1_000_000
    estimated_seconds = permutations / operations_per_second
    
    if estimated_seconds < 1:
        return f"< 1 giây ({permutations:,} hoán vị)"
    elif estimated_seconds < 60:
        return f"~{estimated_seconds:.1f} giây ({permutations:,} hoán vị)"
    elif estimated_seconds < 3600:
        minutes = estimated_seconds / 60
        return f"~{minutes:.1f} phút ({permutations:,} hoán vị)"
    elif estimated_seconds < 86400:
        hours = estimated_seconds / 3600
        return f"~{hours:.1f} giờ ({permutations:,} hoán vị)"
    else:
        days = estimated_seconds / 86400
        return f"~{days:.1f} ngày ({permutations:,} hoán vị)"

def is_brute_force_feasible(n: int, max_time_seconds: int = 60) -> bool:
    """
    Kiểm tra xem thuật toán Brute-force có khả thi không
    
    Args:
        n: Số lượng thành phố
        max_time_seconds: Thời gian tối đa chấp nhận được (giây)
    
    Returns:
        True nếu khả thi, False nếu không
    """
    import math
    
    if n <= 1:
        return True
    
    permutations = math.factorial(n - 1)
    operations_per_second = 1_000_000
    estimated_seconds = permutations / operations_per_second
    
    return estimated_seconds <= max_time_seconds

def demonstrate_brute_force_complexity():
    """
    Minh họa độ phức tạp của thuật toán Brute-force
    """
    print("=== MINH HỌA ĐỘ PHỨC TẠP THUẬT TOÁN BRUTE-FORCE ===")
    print(f"{'Số thành phố':<12} {'Số hoán vị':<15} {'Thời gian ước tính':<20}")
    print("-" * 50)
    
    for n in range(3, 16):
        time_estimate = estimate_brute_force_time(n)
        permutations = math.factorial(n - 1) if n > 1 else 1
        feasible = "✓" if is_brute_force_feasible(n) else "✗"
        
        print(f"{n:<12} {permutations:<15,} {time_estimate:<20} {feasible}")

if __name__ == "__main__":
    # Test với một ví dụ nhỏ
    test_matrix = [
        [0, 10, 15, 5],
        [10, 0, 35, 25],
        [15, 35, 0, 30],
        [5, 25, 30, 0]
    ]
    
    print("Ma trận test:")
    for i, row in enumerate(test_matrix):
        print(f"  {i}: {row}")
    
    print("\nGiải bằng Brute-force thông thường:")
    tour, cost = brute_force_tsp(test_matrix)
    print(f"Chu trình tối ưu: {' → '.join(map(str, tour))}")
    print(f"Chi phí tối ưu: {cost}")
    
    print("\nGiải bằng Brute-force với pruning:")
    tour2, cost2 = brute_force_tsp_with_pruning(test_matrix)
    print(f"Chu trình tối ưu: {' → '.join(map(str, tour2))}")
    print(f"Chi phí tối ưu: {cost2}")
    
    print("\nMinh họa độ phức tạp:")
    demonstrate_brute_force_complexity()
