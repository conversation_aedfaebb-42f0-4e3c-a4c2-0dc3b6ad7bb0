"""
Demo chương trình TSP - Ch<PERSON>y thử nhanh các tính năng
"""

import sys
import os

# Thê<PERSON> các thư mục cần thiết vào path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'data'))

from brute_force import brute_force_tsp, estimate_brute_force_time, demonstrate_brute_force_complexity
from nearest_neighbor import nearest_neighbor_tsp, nearest_neighbor_all_starts, analyze_nearest_neighbor_quality, print_nearest_neighbor_analysis
from utils import print_distance_matrix, print_tour_result, compare_results, measure_execution_time
from test_cases import ALL_TEST_CASES, print_test_case_info

def demo_basic_functionality():
    """Demo chức năng cơ bản của chương trình"""
    print("=" * 70)
    print("DEMO CHỨC NĂNG CƠ BẢN - BÀI TOÁN TSP")
    print("=" * 70)
    
    # Sử dụng test case 4 thành phố
    test_case = ALL_TEST_CASES[0]  # TEST_4_CITIES
    matrix = test_case['matrix']
    
    print("Sử dụng bộ dữ liệu mẫu:")
    print_test_case_info(test_case)
    
    print("\n" + "="*50)
    print("CHẠY THUẬT TOÁN BRUTE-FORCE")
    print("="*50)
    
    # Chạy Brute-force
    tour_bf, time_bf = measure_execution_time(brute_force_tsp, matrix)
    cost_bf = tour_bf[1]
    tour_bf = tour_bf[0]
    print_tour_result(tour_bf, cost_bf, time_bf, "Brute-force")
    
    print("\n" + "="*50)
    print("CHẠY THUẬT TOÁN NEAREST NEIGHBOR")
    print("="*50)
    
    # Chạy Nearest Neighbor
    tour_nn, time_nn = measure_execution_time(nearest_neighbor_all_starts, matrix)
    cost_nn = tour_nn[1]
    tour_nn = tour_nn[0]
    print_tour_result(tour_nn, cost_nn, time_nn, "Nearest Neighbor")
    
    # So sánh kết quả
    results = [
        ("Brute-force", tour_bf, cost_bf, time_bf),
        ("Nearest Neighbor", tour_nn, cost_nn, time_nn)
    ]
    compare_results(results)

def demo_performance_comparison():
    """Demo so sánh hiệu suất các thuật toán"""
    print("\n" + "=" * 70)
    print("DEMO SO SÁNH HIỆU SUẤT")
    print("=" * 70)
    
    print("So sánh thời gian thực thi với các kích thước khác nhau:")
    print()
    
    # Test với các kích thước 4, 5, 6
    sizes_to_test = [4, 5, 6]
    
    print(f"{'Kích thước':<10} {'Brute-force':<15} {'Nearest Neighbor':<18} {'Tỷ lệ tốc độ':<12}")
    print("-" * 60)
    
    for size in sizes_to_test:
        # Tìm test case phù hợp
        test_case = None
        for tc in ALL_TEST_CASES:
            if len(tc['matrix']) == size:
                test_case = tc
                break
        
        if test_case is None:
            continue
        
        matrix = test_case['matrix']
        
        # Đo thời gian Brute-force
        _, time_bf = measure_execution_time(brute_force_tsp, matrix)
        
        # Đo thời gian Nearest Neighbor
        _, time_nn = measure_execution_time(nearest_neighbor_tsp, matrix)
        
        # Tính tỷ lệ
        ratio = time_bf / time_nn if time_nn > 0 else float('inf')
        
        print(f"{size:<10} {time_bf:<15.6f} {time_nn:<18.6f} {ratio:<12.1f}x")

def demo_algorithm_analysis():
    """Demo phân tích chi tiết thuật toán"""
    print("\n" + "=" * 70)
    print("DEMO PHÂN TÍCH CHI TIẾT THUẬT TOÁN")
    print("=" * 70)
    
    # Sử dụng test case khó cho Nearest Neighbor
    test_case = None
    for tc in ALL_TEST_CASES:
        if "Khó cho Nearest Neighbor" in tc['name']:
            test_case = tc
            break
    
    if test_case is None:
        test_case = ALL_TEST_CASES[1]  # Fallback
    
    matrix = test_case['matrix']
    optimal_cost = test_case['optimal_cost']
    
    print(f"Phân tích với bộ dữ liệu: {test_case['name']}")
    print_distance_matrix(matrix)
    
    # Phân tích Nearest Neighbor
    print("\n--- PHÂN TÍCH NEAREST NEIGHBOR ---")
    analysis = analyze_nearest_neighbor_quality(matrix, optimal_cost)
    print_nearest_neighbor_analysis(analysis)

def demo_complexity_analysis():
    """Demo phân tích độ phức tạp"""
    print("\n" + "=" * 70)
    print("DEMO PHÂN TÍCH ĐỘ PHỨC TẠP")
    print("=" * 70)
    
    demonstrate_brute_force_complexity()
    
    print("\nGiải thích:")
    print("- Brute-force có độ phức tạp O(n!) - tăng rất nhanh")
    print("- Nearest Neighbor có độ phức tạp O(n²) - tăng chậm")
    print("- Với n > 10, Brute-force trở nên không khả thi")

def demo_different_test_cases():
    """Demo với các test cases khác nhau"""
    print("\n" + "=" * 70)
    print("DEMO VỚI CÁC BỘ DỮ LIỆU KHÁC NHAU")
    print("=" * 70)
    
    # Chọn một số test cases đặc biệt
    special_cases = [
        "Ma trận đối xứng hoàn hảo",
        "Khó cho Nearest Neighbor",
        "Thành phố Việt Nam (đơn giản)"
    ]
    
    for case_name in special_cases:
        test_case = None
        for tc in ALL_TEST_CASES:
            if case_name in tc['name']:
                test_case = tc
                break
        
        if test_case is None:
            continue
        
        print(f"\n--- {test_case['name'].upper()} ---")
        matrix = test_case['matrix']
        
        # Chỉ chạy Nearest Neighbor để tiết kiệm thời gian
        tour, cost = nearest_neighbor_all_starts(matrix)
        optimal = test_case['optimal_cost']
        ratio = cost / optimal
        
        print(f"Kết quả Nearest Neighbor: {cost}")
        print(f"Lời giải tối ưu: {optimal}")
        print(f"Tỷ lệ xấp xỉ: {ratio:.3f} ({ratio*100:.1f}%)")

def demo_interactive_features():
    """Demo các tính năng tương tác"""
    print("\n" + "=" * 70)
    print("DEMO TÍNH NĂNG TƯƠNG TÁC")
    print("=" * 70)
    
    print("Chương trình hỗ trợ các tính năng sau:")
    print("1. Nhập dữ liệu từ người dùng")
    print("2. Sinh dữ liệu ngẫu nhiên")
    print("3. Chọn thuật toán để chạy")
    print("4. So sánh kết quả các thuật toán")
    print("5. Lưu kết quả ra file")
    print("6. Phân tích chi tiết thuật toán")
    
    print("\nĐể sử dụng đầy đủ các tính năng, chạy:")
    print("python src/tsp_solver.py")

def main():
    """Hàm chính của demo"""
    print("🚀 CHÀO MỪNG ĐẾN VỚI DEMO CHƯƠNG TRÌNH TSP!")
    print("Demo này sẽ trình bày các tính năng chính của chương trình.")
    print()
    
    try:
        # Chạy các demo
        demo_basic_functionality()
        demo_performance_comparison()
        demo_algorithm_analysis()
        demo_complexity_analysis()
        demo_different_test_cases()
        demo_interactive_features()
        
        print("\n" + "=" * 70)
        print("🎉 DEMO HOÀN THÀNH!")
        print("=" * 70)
        print("Để chạy chương trình đầy đủ với giao diện tương tác:")
        print("  python src/tsp_solver.py")
        print()
        print("Để chạy kiểm thử thuật toán:")
        print("  python tests/test_algorithms.py")
        print()
        print("Để xem báo cáo chi tiết:")
        print("  Mở file report/TSP_Report.md")
        
    except KeyboardInterrupt:
        print("\n\nDemo bị ngắt bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi trong quá trình demo: {e}")
        print("Vui lòng kiểm tra lại cài đặt và thử lại.")

if __name__ == "__main__":
    main()
