# HƯỚNG DẪN CÀI ĐẶT VÀ SỬ DỤNG CHƯƠNG TRÌNH TSP

## 📋 <PERSON><PERSON><PERSON> lục
1. [<PERSON><PERSON><PERSON> c<PERSON><PERSON> hệ thống](#yêu-c<PERSON>u-hệ-thống)
2. [<PERSON><PERSON><PERSON> đặt](#cài-đặt)
3. [<PERSON><PERSON><PERSON> tr<PERSON><PERSON> thư mục](#cấu-trú<PERSON>-thư-mục)
4. [Hướng dẫn sử dụng](#hướng-dẫn-sử-dụng)
5. [<PERSON><PERSON><PERSON> t<PERSON>h năng chính](#các-t<PERSON>h-năng-ch<PERSON>h)
6. [<PERSON><PERSON> dụ sử dụng](#ví-dụ-sử-dụng)
7. [<PERSON>h<PERSON><PERSON> phục sự cố](#khắc-phục-sự-cố)

## 🔧 Yêu cầu hệ thống

### Phần mềm cần thiết:
- **Python 3.7 trở lên** (khuyến nghị Python 3.8+)
- **Hệ điều hành**: Windows, macOS, hoặc Linux

### Th<PERSON> viện Python (t<PERSON><PERSON> ch<PERSON>):
```bash
# <PERSON><PERSON><PERSON> thư viện cơ bản (đã có sẵn trong Python)
- itertools
- time
- random
- sys
- os

# Th<PERSON> viện mở rộng (tùy chọn)
pip install numpy matplotlib
```

## 📦 Cài đặt

### Bước 1: Tải mã nguồn
```bash
# Nếu có Git
git clone [repository-url]
cd TSP_Report

# Hoặc tải file ZIP và giải nén
```

### Bước 2: Kiểm tra Python
```bash
python --version
# hoặc
python3 --version
```

### Bước 3: Kiểm tra cài đặt
```bash
# Chạy demo để kiểm tra
python demo.py

# Chạy kiểm thử
python tests/test_algorithms.py
```

## 📁 Cấu trúc thư mục

```
TSP_Report/
├── README.md                    # Thông tin tổng quan
├── HUONG_DAN.md                # File này
├── demo.py                     # Demo nhanh chương trình
├── report/
│   ├── TSP_Report.md           # Báo cáo chính (đọc đầu tiên)
│   └── images/                 # Hình ảnh minh họa
├── src/                        # Mã nguồn chính
│   ├── tsp_solver.py           # Chương trình chính
│   ├── brute_force.py          # Thuật toán Brute-force
│   ├── nearest_neighbor.py     # Thuật toán Nearest Neighbor
│   └── utils.py                # Các hàm tiện ích
├── data/                       # Dữ liệu và test cases
│   ├── test_cases.py           # Bộ dữ liệu kiểm thử
│   └── sample_data/            # Dữ liệu mẫu
└── tests/
    └── test_algorithms.py      # Kiểm thử thuật toán
```

## 🚀 Hướng dẫn sử dụng

### Chạy chương trình chính:
```bash
cd TSP_Report
python src/tsp_solver.py
```

### Chạy demo nhanh:
```bash
python demo.py
```

### Chạy kiểm thử:
```bash
python tests/test_algorithms.py
```

### Chạy từng module riêng lẻ:
```bash
# Test thuật toán Brute-force
python src/brute_force.py

# Test thuật toán Nearest Neighbor
python src/nearest_neighbor.py

# Test dữ liệu
python data/test_cases.py
```

## ⭐ Các tính năng chính

### 1. Nhập dữ liệu đa dạng
- **Sinh ngẫu nhiên**: Tạo ma trận khoảng cách ngẫu nhiên
- **Nhập thủ công**: Nhập từ bàn phím
- **Dữ liệu mẫu**: Sử dụng các bộ test có sẵn

### 2. Thuật toán giải TSP
- **Brute-force**: Tìm lời giải tối ưu (n ≤ 10)
- **Nearest Neighbor**: Tìm lời giải gần đúng nhanh chóng
- **So sánh hiệu quả**: Đánh giá thời gian và chất lượng

### 3. Phân tích kết quả
- **Hiển thị chu trình**: Trực quan và dễ hiểu
- **So sánh thuật toán**: Bảng so sánh chi tiết
- **Thống kê hiệu suất**: Thời gian, tỷ lệ xấp xỉ

### 4. Xuất kết quả
- **Lưu file**: Xuất kết quả ra file text
- **Báo cáo chi tiết**: Thông tin đầy đủ về quá trình giải

## 📖 Ví dụ sử dụng

### Ví dụ 1: Chạy với dữ liệu mẫu
```bash
python src/tsp_solver.py
# Chọn: 3 (Sử dụng dữ liệu mẫu)
# Chọn: 1 (4 thành phố - dễ)
# Chọn: 3 (So sánh cả hai thuật toán)
```

### Ví dụ 2: Sinh dữ liệu ngẫu nhiên
```bash
python src/tsp_solver.py
# Chọn: 1 (Sinh dữ liệu ngẫu nhiên)
# Nhập: 6 (số thành phố)
# Chọn: 2 (Nearest Neighbor)
```

### Ví dụ 3: Nhập dữ liệu thủ công
```bash
python src/tsp_solver.py
# Chọn: 2 (Nhập dữ liệu thủ công)
# Nhập: 4 (số thành phố)
# Nhập ma trận 4x4:
# Hàng 0: 0 10 15 5
# Hàng 1: 10 0 35 25
# Hàng 2: 15 35 0 30
# Hàng 3: 5 25 30 0
```

### Ví dụ kết quả mẫu:
```
=== KẾT QUẢ THUẬT TOÁN BRUTE-FORCE ===
Chu trình tìm được: 0 → 3 → 1 → 2 → 0
Chi phí: 80
Thời gian thực thi: 0.001234 giây

=== KẾT QUẢ THUẬT TOÁN NEAREST NEIGHBOR ===
Chu trình tìm được: 0 → 3 → 1 → 2 → 0
Chi phí: 80
Thời gian thực thi: 0.000123 giây

=== SO SÁNH KẾT QUẢ ===
Thuật toán          Chi phí    Thời gian (s)   Tỷ lệ      Tốc độ    
----------------------------------------------------------------------
Brute Force         80         0.001234        100.0%     1.0x      
Nearest Neighbor    80         0.000123        100.0%     10.0x     

Chi phí tốt nhất: 80
Thời gian nhanh nhất: 0.000123 giây
```

## 🔍 Khắc phục sự cố

### Lỗi thường gặp:

#### 1. "ModuleNotFoundError"
```bash
# Nguyên nhân: Không tìm thấy module
# Giải pháp: Chạy từ thư mục gốc
cd TSP_Report
python src/tsp_solver.py
```

#### 2. "Python không được nhận diện"
```bash
# Trên Windows, thử:
py src/tsp_solver.py

# Hoặc:
python3 src/tsp_solver.py
```

#### 3. Chương trình chạy quá chậm
```bash
# Nguyên nhân: Chạy Brute-force với n > 10
# Giải pháp: Chọn Nearest Neighbor hoặc giảm số thành phố
```

#### 4. Lỗi nhập dữ liệu
```bash
# Đảm bảo:
# - Ma trận vuông (n×n)
# - Đường chéo chính = 0
# - Ma trận đối xứng
# - Không có giá trị âm
```

### Kiểm tra hệ thống:
```bash
# Kiểm tra Python
python --version

# Kiểm tra các module cần thiết
python -c "import itertools, time, random, sys, os; print('OK')"

# Chạy test để kiểm tra
python tests/test_algorithms.py
```

## 📚 Tài liệu tham khảo

### Đọc thêm:
1. **Báo cáo chính**: `report/TSP_Report.md`
2. **Mã nguồn**: Các file trong thư mục `src/`
3. **Dữ liệu test**: `data/test_cases.py`

### Liên hệ hỗ trợ:
- Đọc kỹ báo cáo trong `report/TSP_Report.md`
- Chạy `python demo.py` để xem demo
- Kiểm tra `tests/test_algorithms.py` để hiểu cách hoạt động

## 🎯 Mẹo sử dụng hiệu quả

1. **Bắt đầu với demo**: Chạy `python demo.py` trước
2. **Test với dữ liệu nhỏ**: Bắt đầu với 4-5 thành phố
3. **So sánh thuật toán**: Luôn chọn "So sánh cả hai" khi có thể
4. **Lưu kết quả**: Chọn lưu file để phân tích sau
5. **Đọc báo cáo**: Tham khảo `report/TSP_Report.md` để hiểu lý thuyết

---

**Chúc bạn sử dụng chương trình thành công! 🎉**
