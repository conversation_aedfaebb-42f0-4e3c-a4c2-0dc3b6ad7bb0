"""
<PERSON><PERSON><PERSON> hàm tiện ích cho chương trình TSP
"""

import random
import time
from typing import List, Tuple, Optional

def generate_random_distance_matrix(n: int, min_distance: int = 10, max_distance: int = 100) -> List[List[int]]:
    """
    Sinh ma trận khoảng cách ngẫu nhiên cho n thành phố
    
    Args:
        n: Số lượng thành phố
        min_distance: K<PERSON>ảng cách tối thiểu
        max_distance: Khoảng cách tối đa
    
    Returns:
        Ma trận khoảng cách đối xứng n×n
    """
    matrix = [[0] * n for _ in range(n)]
    
    for i in range(n):
        for j in range(i + 1, n):
            distance = random.randint(min_distance, max_distance)
            matrix[i][j] = distance
            matrix[j][i] = distance  # Đảm bảo tính đối xứng
    
    return matrix

def read_distance_matrix_from_input(n: int) -> List[List[int]]:
    """
    <PERSON><PERSON><PERSON> ma trận khoảng cách từ input của người dùng
    
    Args:
        n: <PERSON><PERSON> lượng thành phố
    
    Returns:
        Ma trận khoảng cách n×n
    """
    print(f"Nhập ma trận khoảng cách {n}×{n}:")
    print("(Nhập 0 cho đường chéo chính, các giá trị khác cho khoảng cách)")
    
    matrix = []
    for i in range(n):
        while True:
            try:
                row_input = input(f"Hàng {i}: ").strip()
                row = list(map(int, row_input.split()))
                if len(row) != n:
                    print(f"Cần nhập đúng {n} số. Vui lòng nhập lại.")
                    continue
                matrix.append(row)
                break
            except ValueError:
                print("Vui lòng nhập các số nguyên, cách nhau bởi dấu cách.")
    
    # Đảm bảo tính đối xứng
    for i in range(n):
        for j in range(n):
            if i != j and matrix[i][j] != matrix[j][i]:
                print(f"Cảnh báo: Ma trận không đối xứng tại ({i},{j})")
                matrix[j][i] = matrix[i][j]
    
    return matrix

def calculate_tour_cost(tour: List[int], distance_matrix: List[List[int]]) -> int:
    """
    Tính tổng chi phí của một chu trình
    
    Args:
        tour: Danh sách các thành phố trong chu trình
        distance_matrix: Ma trận khoảng cách
    
    Returns:
        Tổng chi phí của chu trình
    """
    total_cost = 0
    n = len(tour)
    
    for i in range(n - 1):
        total_cost += distance_matrix[tour[i]][tour[i + 1]]
    
    return total_cost

def print_distance_matrix(matrix: List[List[int]], title: str = "Ma trận khoảng cách") -> None:
    """
    In ma trận khoảng cách một cách đẹp mắt
    
    Args:
        matrix: Ma trận khoảng cách
        title: Tiêu đề của ma trận
    """
    n = len(matrix)
    print(f"\n{title}:")
    
    # In header
    print("     ", end="")
    for j in range(n):
        print(f"{j:4}", end="")
    print()
    
    # In các hàng
    for i in range(n):
        print(f"{i:2}   ", end="")
        for j in range(n):
            if i == j:
                print("   0", end="")
            else:
                print(f"{matrix[i][j]:4}", end="")
        print()

def print_tour_result(tour: List[int], cost: int, execution_time: float, algorithm_name: str) -> None:
    """
    In kết quả của một chu trình
    
    Args:
        tour: Chu trình tìm được
        cost: Chi phí của chu trình
        execution_time: Thời gian thực thi
        algorithm_name: Tên thuật toán
    """
    print(f"\n=== KẾT QUẢ THUẬT TOÁN {algorithm_name.upper()} ===")
    print(f"Chu trình tìm được: {' → '.join(map(str, tour))}")
    print(f"Chi phí: {cost}")
    print(f"Thời gian thực thi: {execution_time:.6f} giây")

def compare_results(results: List[Tuple[str, List[int], int, float]]) -> None:
    """
    So sánh kết quả của các thuật toán
    
    Args:
        results: Danh sách tuple (tên_thuật_toán, chu_trình, chi_phí, thời_gian)
    """
    if len(results) < 2:
        return
    
    print(f"\n{'='*60}")
    print("SO SÁNH KẾT QUẢ CÁC THUẬT TOÁN")
    print(f"{'='*60}")
    
    # Tìm kết quả tốt nhất
    best_cost = min(result[2] for result in results)
    fastest_time = min(result[3] for result in results)
    
    print(f"{'Thuật toán':<20} {'Chi phí':<10} {'Thời gian (s)':<15} {'Tỷ lệ':<10} {'Tốc độ':<10}")
    print("-" * 70)
    
    for name, tour, cost, exec_time in results:
        ratio = f"{(cost / best_cost * 100):.1f}%" if best_cost > 0 else "N/A"
        speed_ratio = f"{(fastest_time / exec_time):.1f}x" if exec_time > 0 else "N/A"
        
        print(f"{name:<20} {cost:<10} {exec_time:<15.6f} {ratio:<10} {speed_ratio:<10}")
    
    print(f"\nChi phí tốt nhất: {best_cost}")
    print(f"Thời gian nhanh nhất: {fastest_time:.6f} giây")

def validate_distance_matrix(matrix: List[List[int]]) -> bool:
    """
    Kiểm tra tính hợp lệ của ma trận khoảng cách
    
    Args:
        matrix: Ma trận khoảng cách cần kiểm tra
    
    Returns:
        True nếu ma trận hợp lệ, False nếu không
    """
    n = len(matrix)
    
    # Kiểm tra ma trận vuông
    for row in matrix:
        if len(row) != n:
            print("Lỗi: Ma trận không vuông")
            return False
    
    # Kiểm tra đường chéo chính
    for i in range(n):
        if matrix[i][i] != 0:
            print(f"Lỗi: Khoảng cách từ thành phố {i} đến chính nó phải bằng 0")
            return False
    
    # Kiểm tra tính đối xứng
    for i in range(n):
        for j in range(n):
            if matrix[i][j] != matrix[j][i]:
                print(f"Lỗi: Ma trận không đối xứng tại ({i},{j})")
                return False
    
    # Kiểm tra giá trị âm
    for i in range(n):
        for j in range(n):
            if matrix[i][j] < 0:
                print(f"Lỗi: Khoảng cách âm tại ({i},{j})")
                return False
    
    return True

def get_user_choice(prompt: str, valid_choices: List[str]) -> str:
    """
    Lấy lựa chọn từ người dùng với validation
    
    Args:
        prompt: Câu hỏi cho người dùng
        valid_choices: Danh sách các lựa chọn hợp lệ
    
    Returns:
        Lựa chọn hợp lệ của người dùng
    """
    while True:
        choice = input(prompt).strip()
        if choice in valid_choices:
            return choice
        print(f"Lựa chọn không hợp lệ. Vui lòng chọn một trong: {', '.join(valid_choices)}")

def measure_execution_time(func, *args, **kwargs) -> Tuple[any, float]:
    """
    Đo thời gian thực thi của một hàm
    
    Args:
        func: Hàm cần đo thời gian
        *args: Các tham số của hàm
        **kwargs: Các tham số keyword của hàm
    
    Returns:
        Tuple (kết_quả_hàm, thời_gian_thực_thi)
    """
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    execution_time = end_time - start_time
    
    return result, execution_time

def save_results_to_file(results: List[Tuple[str, List[int], int, float]], 
                        distance_matrix: List[List[int]], 
                        filename: str = "tsp_results.txt") -> None:
    """
    Lưu kết quả vào file
    
    Args:
        results: Danh sách kết quả các thuật toán
        distance_matrix: Ma trận khoảng cách
        filename: Tên file để lưu
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== KẾT QUẢ GIẢI BÀI TOÁN TSP ===\n\n")
            
            # Ghi ma trận khoảng cách
            n = len(distance_matrix)
            f.write(f"Số thành phố: {n}\n")
            f.write("Ma trận khoảng cách:\n")
            for i, row in enumerate(distance_matrix):
                f.write(f"  {i}: {' '.join(f'{x:3}' for x in row)}\n")
            f.write("\n")
            
            # Ghi kết quả các thuật toán
            for name, tour, cost, exec_time in results:
                f.write(f"=== {name.upper()} ===\n")
                f.write(f"Chu trình: {' → '.join(map(str, tour))}\n")
                f.write(f"Chi phí: {cost}\n")
                f.write(f"Thời gian: {exec_time:.6f} giây\n\n")
            
            # Ghi so sánh
            if len(results) > 1:
                best_cost = min(result[2] for result in results)
                f.write("=== SO SÁNH ===\n")
                for name, _, cost, exec_time in results:
                    ratio = cost / best_cost * 100 if best_cost > 0 else 0
                    f.write(f"{name}: {cost} ({ratio:.1f}% so với tối ưu), {exec_time:.6f}s\n")
        
        print(f"Đã lưu kết quả vào file: {filename}")
    
    except Exception as e:
        print(f"Lỗi khi lưu file: {e}")
