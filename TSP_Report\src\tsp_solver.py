"""
Chương trình chính giải bài toán TSP
Tích hợp các thuật toán <PERSON>rute-force và Nearest Neighbor
"""

import sys
import os
from typing import List, Tuple, Optional

# Thêm thư mục hiện tại vào path để import các module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import (
    generate_random_distance_matrix, 
    read_distance_matrix_from_input,
    print_distance_matrix,
    print_tour_result,
    compare_results,
    validate_distance_matrix,
    get_user_choice,
    measure_execution_time,
    save_results_to_file
)
from brute_force import brute_force_tsp, is_brute_force_feasible, estimate_brute_force_time
from nearest_neighbor import nearest_neighbor_tsp, nearest_neighbor_all_starts, analyze_nearest_neighbor_quality, print_nearest_neighbor_analysis

def print_welcome():
    """In thông điệp chào mừng"""
    print("=" * 70)
    print("    CHƯƠNG TRÌNH GIẢI BÀI TOÁN TRAVELING SALESMAN PROBLEM (TSP)")
    print("=" * 70)
    print("Chương trình này minh họa hai thuật toán giải bài toán TSP:")
    print("1. Brute-force: Tìm lời giải tối ưu (chỉ phù hợp với n ≤ 10)")
    print("2. Nearest Neighbor: Tìm lời giải gần đúng nhanh chóng")
    print("=" * 70)

def get_input_method() -> str:
    """Lấy phương thức nhập dữ liệu từ người dùng"""
    print("\nChọn phương thức nhập dữ liệu:")
    print("1. Sinh dữ liệu ngẫu nhiên")
    print("2. Nhập dữ liệu thủ công")
    print("3. Sử dụng dữ liệu mẫu")
    
    return get_user_choice("Lựa chọn của bạn (1-3): ", ["1", "2", "3"])

def get_number_of_cities() -> int:
    """Lấy số lượng thành phố từ người dùng"""
    while True:
        try:
            n = int(input("Nhập số lượng thành phố (3-20): "))
            if 3 <= n <= 20:
                return n
            else:
                print("Số lượng thành phố phải từ 3 đến 20.")
        except ValueError:
            print("Vui lòng nhập một số nguyên hợp lệ.")

def get_sample_data() -> List[List[int]]:
    """Trả về dữ liệu mẫu để test"""
    print("\nChọn bộ dữ liệu mẫu:")
    print("1. 4 thành phố (dễ)")
    print("2. 5 thành phố (trung bình)")
    print("3. 6 thành phố (khó)")
    
    choice = get_user_choice("Lựa chọn (1-3): ", ["1", "2", "3"])
    
    if choice == "1":
        return [
            [0, 10, 15, 5],
            [10, 0, 35, 25],
            [15, 35, 0, 30],
            [5, 25, 30, 0]
        ]
    elif choice == "2":
        return [
            [0, 12, 10, 19, 8],
            [12, 0, 3, 7, 2],
            [10, 3, 0, 6, 20],
            [19, 7, 6, 0, 4],
            [8, 2, 20, 4, 0]
        ]
    else:  # choice == "3"
        return [
            [0, 20, 42, 25, 30, 15],
            [20, 0, 30, 34, 15, 25],
            [42, 30, 0, 12, 45, 35],
            [25, 34, 12, 0, 20, 30],
            [30, 15, 45, 20, 0, 18],
            [15, 25, 35, 30, 18, 0]
        ]

def get_distance_matrix() -> List[List[int]]:
    """Lấy ma trận khoảng cách từ người dùng"""
    method = get_input_method()
    
    if method == "1":  # Sinh ngẫu nhiên
        n = get_number_of_cities()
        print(f"\nSinh dữ liệu ngẫu nhiên cho {n} thành phố...")
        matrix = generate_random_distance_matrix(n)
        
    elif method == "2":  # Nhập thủ công
        n = get_number_of_cities()
        matrix = read_distance_matrix_from_input(n)
        
    else:  # method == "3" - Dữ liệu mẫu
        matrix = get_sample_data()
    
    # Kiểm tra tính hợp lệ
    if not validate_distance_matrix(matrix):
        print("Dữ liệu không hợp lệ. Sử dụng dữ liệu mẫu.")
        matrix = get_sample_data()
    
    return matrix

def choose_algorithms(n: int) -> List[str]:
    """Cho phép người dùng chọn thuật toán để chạy"""
    print(f"\nChọn thuật toán để giải bài toán {n} thành phố:")
    
    # Kiểm tra khả thi của Brute-force
    if is_brute_force_feasible(n):
        print("1. Brute-force (tìm lời giải tối ưu)")
        print("2. Nearest Neighbor (tìm lời giải gần đúng)")
        print("3. So sánh cả hai thuật toán")
        choice = get_user_choice("Lựa chọn (1-3): ", ["1", "2", "3"])
        
        if choice == "1":
            return ["brute_force"]
        elif choice == "2":
            return ["nearest_neighbor"]
        else:
            return ["brute_force", "nearest_neighbor"]
    else:
        print(f"Cảnh báo: Brute-force không khả thi với {n} thành phố")
        print(f"Thời gian ước tính: {estimate_brute_force_time(n)}")
        print("1. Chỉ chạy Nearest Neighbor")
        print("2. Vẫn chạy Brute-force (có thể mất rất lâu)")
        choice = get_user_choice("Lựa chọn (1-2): ", ["1", "2"])
        
        if choice == "1":
            return ["nearest_neighbor"]
        else:
            return ["brute_force"]

def run_algorithms(distance_matrix: List[List[int]], algorithms: List[str]) -> List[Tuple[str, List[int], int, float]]:
    """Chạy các thuật toán được chọn"""
    results = []
    n = len(distance_matrix)
    
    for algorithm in algorithms:
        print(f"\n{'='*50}")
        print(f"ĐANG CHẠY THUẬT TOÁN {algorithm.upper().replace('_', '-')}")
        print(f"{'='*50}")
        
        if algorithm == "brute_force":
            if n > 10:
                confirm = get_user_choice(
                    f"Bạn có chắc muốn chạy Brute-force với {n} thành phố? (y/n): ",
                    ["y", "n", "Y", "N"]
                )
                if confirm.lower() == "n":
                    print("Bỏ qua thuật toán Brute-force.")
                    continue
            
            tour, execution_time = measure_execution_time(brute_force_tsp, distance_matrix)
            cost = tour[1]
            tour = tour[0]
            
        elif algorithm == "nearest_neighbor":
            # Chạy từ tất cả các thành phố xuất phát để có kết quả tốt nhất
            tour, execution_time = measure_execution_time(nearest_neighbor_all_starts, distance_matrix)
            cost = tour[1]
            tour = tour[0]
        
        # Lưu kết quả
        results.append((algorithm.replace('_', ' ').title(), tour, cost, execution_time))
        
        # In kết quả
        print_tour_result(tour, cost, execution_time, algorithm.replace('_', ' '))
    
    return results

def analyze_results(distance_matrix: List[List[int]], results: List[Tuple[str, List[int], int, float]]):
    """Phân tích chi tiết kết quả"""
    if not results:
        return
    
    print(f"\n{'='*60}")
    print("PHÂN TÍCH CHI TIẾT KẾT QUẢ")
    print(f"{'='*60}")
    
    # Tìm lời giải tối ưu (nếu có Brute-force)
    optimal_cost = None
    for name, tour, cost, time in results:
        if "Brute Force" in name:
            optimal_cost = cost
            break
    
    # Phân tích Nearest Neighbor nếu có
    for name, tour, cost, time in results:
        if "Nearest Neighbor" in name:
            print("\n--- PHÂN TÍCH NEAREST NEIGHBOR ---")
            analysis = analyze_nearest_neighbor_quality(distance_matrix, optimal_cost)
            print_nearest_neighbor_analysis(analysis)
            break

def main():
    """Hàm chính của chương trình"""
    print_welcome()
    
    try:
        # Lấy dữ liệu đầu vào
        distance_matrix = get_distance_matrix()
        n = len(distance_matrix)
        
        # Hiển thị ma trận khoảng cách
        print_distance_matrix(distance_matrix)
        
        # Chọn thuật toán
        algorithms = choose_algorithms(n)
        
        # Chạy thuật toán
        results = run_algorithms(distance_matrix, algorithms)
        
        # So sánh kết quả
        if len(results) > 1:
            compare_results(results)
        
        # Phân tích chi tiết
        analyze_results(distance_matrix, results)
        
        # Lưu kết quả
        save_choice = get_user_choice("\nBạn có muốn lưu kết quả vào file? (y/n): ", ["y", "n", "Y", "N"])
        if save_choice.lower() == "y":
            filename = input("Nhập tên file (để trống để dùng tên mặc định): ").strip()
            if not filename:
                filename = f"tsp_results_{n}_cities.txt"
            save_results_to_file(results, distance_matrix, filename)
        
        print(f"\n{'='*60}")
        print("CẢM ƠN BẠN ĐÃ SỬ DỤNG CHƯƠNG TRÌNH!")
        print(f"{'='*60}")
        
    except KeyboardInterrupt:
        print("\n\nChương trình bị ngắt bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi không mong muốn: {e}")
        print("Vui lòng thử lại hoặc liên hệ với nhà phát triển.")

if __name__ == "__main__":
    main()
